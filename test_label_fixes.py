#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لتصحيحات تصميم الملصق
"""

import json
from label_design import create_compact_label_image
from datetime import datetime

def test_label_fixes():
    """اختبار التصحيحات على تصميم الملصق"""
    
    # إعداد البيانات التجريبية
    config = {
        "font_bold_name": "NotoNaskhArabic-Bold.ttf",
        "font_regular_name": "NotoNaskhArabic-Regular.ttf", 
        "font_english_bold": "arial.ttf",
        "font_english_regular": "arial.ttf",
        "label_width_mm": 50,
        "label_height_mm": 25,
        "dpi": 300,
        "padding_mm": 0.5,
        "line_padding_px": 2,
        "section_gap_px": 6,
        "barcode_height_mm": 7.0,
        "barcode_width_factor": 1.3,
        "font_size_pt_patient": 11,
        "font_size_pt_id": 10,
        "font_size_pt_regular": 9,
        "font_size_pt_tests": 10,
        "text_color": "#000000",
        "bg_color": "white"
    }
    
    # بيانات تجريبية مع اسم طويل وتحاليل كثيرة
    lab_order_info = {
        'patient': {
            'name': 'محمد عبد الرحمن أحمد الطويل الاسم جداً'  # اسم طويل لاختبار المشكلة
        },
        'created_at_raw': datetime.now().isoformat() + 'Z'
    }
    
    sample_group = {
        'sample_code': 'LAB001',
        'name': 'Blood',
        'tests': [
            {'name': 'CBC'},
            {'name': 'ESR'},
            {'name': 'CRP'},
            {'name': 'Glucose'},
            {'name': 'Creatinine'},
            {'name': 'Urea'},
            {'name': 'ALT'},
            {'name': 'AST'},
            {'name': 'Bilirubin'},
            {'name': 'Albumin'},
            {'name': 'Total Protein'},
            {'name': 'Cholesterol'},
            {'name': 'Triglycerides'},
            {'name': 'HDL'},
            {'name': 'LDL'}  # تحاليل كثيرة لاختبار المساحة
        ]
    }
    
    print("🧪 إنشاء ملصق تجريبي مع التصحيحات...")
    
    try:
        # إنشاء صورة الملصق
        label_image = create_compact_label_image(
            lab_order_info=lab_order_info,
            sample_group=sample_group,
            config=config
        )
        
        # حفظ الصورة للمراجعة
        output_path = "test_label_fixed.png"
        label_image.save(output_path)
        print(f"✅ تم إنشاء الملصق التجريبي بنجاح: {output_path}")
        print(f"📏 أبعاد الصورة: {label_image.width} x {label_image.height} بكسل")
        
        # عرض معلومات التحاليل
        test_names = [test['name'] for test in sample_group['tests']]
        print(f"🔬 عدد التحاليل: {len(test_names)}")
        print(f"📝 التحاليل: {' '.join(test_names)}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الملصق: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار تصحيحات الملصق...")
    success = test_label_fixes()
    
    if success:
        print("\n✅ تم الاختبار بنجاح!")
        print("📋 التصحيحات المطبقة:")
        print("   1. تحسين موضع اسم المريض لتجنب الاختفاء من اليمين")
        print("   2. تقليل الفراغات بين التحاليل لإظهار المزيد")
        print("   3. استخدام مسافات بسيطة بدلاً من الفواصل بين التحاليل")
        print("   4. تقليل المسافة قبل قائمة التحاليل")
        print("\n🖼️ يرجى فحص الملف: test_label_fixed.png")
    else:
        print("\n❌ فشل الاختبار!")
