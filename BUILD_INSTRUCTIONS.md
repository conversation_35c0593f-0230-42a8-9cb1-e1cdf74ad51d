# تعليمات بناء LaraLab Print Agent

## 📋 المتطلبات الأساسية

### 1. Python 3.8+
```bash
python --version
```

### 2. المكتبات المطلوبة
```bash
pip install -r requirements.txt
pip install pyinstaller
```

### 3. أدوات إضافية (اختيارية)
- **NSIS** لإنشاء مثبت Windows متقدم
- **Inno Setup** كبديل لـ NSIS

## 🔨 طرق البناء

### الطريقة 1: البناء التلقائي (الأسهل)
```bash
python build_installer.py
```

هذا الأمر سيقوم بـ:
- ✅ بناء الملف التنفيذي
- ✅ إنشاء مجلد التوزيع
- ✅ نسخ جميع الملفات المطلوبة
- ✅ إنشاء مثبت ZIP

### الطريقة 2: البناء اليدوي

#### خطوة 1: بناء الملف التنفيذي
```bash
pyinstaller gui.spec
```

أو:
```bash
pyinstaller --onefile --windowed --name=LaraLabPrintAgent --add-data="fonts;fonts" --add-data="config.json;." gui.py
```

#### خطوة 2: نسخ الملفات المطلوبة
```bash
mkdir release
copy dist\LaraLabPrintAgent.exe release\
copy README.txt release\
copy TROUBLESHOOTING.md release\
xcopy fonts release\fonts\ /E /I
```

#### خطوة 3: إنشاء ملف إعدادات افتراضي
انسخ `config.json` إلى مجلد `release`

## 📦 إنشاء مثبت متقدم

### باستخدام NSIS
1. قم بتثبيت NSIS من [الموقع الرسمي](https://nsis.sourceforge.io/)
2. أنشئ ملف `.nsi` للمثبت
3. قم بتشغيل NSIS لإنشاء المثبت

### باستخدام Inno Setup
1. قم بتثبيت Inno Setup من [الموقع الرسمي](https://jrsoftware.org/isinfo.php)
2. أنشئ ملف `.iss` للمثبت
3. قم بتشغيل Inno Setup لإنشاء المثبت

## 🚀 التوزيع

### الملفات المطلوبة للتوزيع:
- `LaraLabPrintAgent.exe` - الملف التنفيذي الرئيسي
- `fonts/` - مجلد الخطوط العربية
- `config.json` - ملف الإعدادات الافتراضي
- `README.txt` - دليل الاستخدام
- `TROUBLESHOOTING.md` - دليل حل المشاكل

### طرق التوزيع:
1. **ZIP Archive** - الأسهل للتوزيع السريع
2. **Windows Installer (MSI)** - للتثبيت الاحترافي
3. **NSIS Installer** - مثبت مخصص
4. **Portable Version** - نسخة محمولة

## ⚙️ إعداد البدء التلقائي

بعد التثبيت، يمكن للمستخدم تشغيل:
```bash
python add_to_startup.py
```

أو من داخل البرنامج:
- فتح الإعدادات
- تفعيل "البدء مع Windows"

## 🔧 استكشاف الأخطاء

### مشاكل شائعة في البناء:

#### 1. خطأ في استيراد المكتبات
```bash
pip install --upgrade pyinstaller
pip install --upgrade pillow
```

#### 2. مشكلة في الخطوط
تأكد من وجود مجلد `fonts` مع الخطوط العربية

#### 3. مشكلة في ملف الإعدادات
تأكد من صحة تنسيق JSON في `config.json`

#### 4. مشكلة في أذونات Windows
قم بتشغيل Command Prompt كمدير

### فحص الملف المبني:
```bash
# فحص التبعيات
python -c "import sys; print(sys.executable)"

# اختبار الطباعة
python simple_print_test.py
```

## 📝 ملاحظات مهمة

1. **الخطوط**: تأكد من تضمين الخطوط العربية
2. **الأذونات**: قد يحتاج البرنامج أذونات إدارية للطباعة
3. **مكافح الفيروسات**: قد يحتاج إضافة استثناء
4. **التحديثات**: قم بتحديث رقم الإصدار في كل بناء

## 🎯 نصائح للتوزيع

1. **اختبر على أجهزة مختلفة** قبل التوزيع
2. **أنشئ دليل مستخدم** واضح
3. **وفر دعم فني** للمستخدمين
4. **استخدم توقيع رقمي** للملف التنفيذي (اختياري)

## 📞 الدعم

في حالة مواجهة مشاكل في البناء:
1. تحقق من ملف `TROUBLESHOOTING.md`
2. تأكد من تثبيت جميع المتطلبات
3. جرب البناء على بيئة نظيفة
