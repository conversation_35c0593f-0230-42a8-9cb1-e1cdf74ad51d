# LaraLab Print Agent - دليل الاستخدام السريع

## 🚀 التثبيت السريع (للمستخدم العادي)

### الطريقة الأولى: المثبت التلقائي (الأسهل) ⭐
1. ⬇️ تحميل ملف `LaraLabPrintAgentSetup.exe`
2. 🖱️ النقر المزدوج على الملف
3. ✅ اتباع التعليمات على الشاشة
4. 🎉 انتهى! البرنامج جاهز للاستخدام

### الطريقة الثانية: النسخة المحمولة
1. ⬇️ تحميل ملف `LaraLabPrintAgent_Portable.zip`
2. 📁 فك الضغط في مجلد منفصل
3. 🖱️ تشغيل `LaraLabPrintAgent.exe`
4. ⚙️ تكوين الإعدادات من الواجهة

## ⚙️ الإعداد الأولي

### 1. تكوين الطابعة
- فتح البرنامج
- الذهاب إلى "الإعدادات"
- اختيار الطابعة من القائمة
- حفظ الإعدادات

### 2. اختبار الطباعة
- النقر على "اختبار الطباعة"
- التأكد من طباعة الملصق بشكل صحيح
- تعديل الإعدادات إذا لزم الأمر

## 🔄 البدء التلقائي مع Windows

### من داخل البرنامج:
- فتح "الإعدادات"
- تفعيل "البدء مع Windows"
- إعادة تشغيل الجهاز للتأكد

### يدوياً:
- تشغيل `add_to_startup.py`
- اختيار "إضافة للبدء التلقائي"

## 📋 الاستخدام اليومي

1. **تشغيل تلقائي**: البرنامج يعمل في الخلفية
2. **استقبال الطلبات**: يستقبل طلبات الطباعة تلقائياً
3. **طباعة فورية**: يطبع الملصقات فور الاستلام
4. **إشعارات**: يعرض حالة الطباعة في الشريط

## 🛠️ حل المشاكل السريع

### المشكلة: البرنامج لا يطبع
✅ **الحل**:
1. تأكد من تشغيل الطابعة
2. تحقق من اتصال USB/الشبكة
3. أعد تشغيل البرنامج

### المشكلة: النصوص العربية غير واضحة
✅ **الحل**:
1. تأكد من وجود مجلد `fonts`
2. أعد تشغيل البرنامج
3. جرب طباعة تجريبية

### المشكلة: البرنامج لا يبدأ تلقائياً
✅ **الحل**:
1. تشغيل `add_to_startup.py`
2. اختيار "إضافة للبدء التلقائي"
3. إعادة تشغيل Windows

## 📞 الدعم الفني

للمساعدة المفصلة، راجع:
- 📖 `TROUBLESHOOTING.md` - دليل حل المشاكل الشامل
- 🧪 `simple_print_test.py` - اختبار الطباعة

## 📝 ملاحظات مهمة

- ⚠️ تأكد من تشغيل البرنامج كمدير إذا واجهت مشاكل في الطباعة
- 🔄 أعد تشغيل البرنامج بعد تغيير إعدادات الطابعة
- 💾 احتفظ بنسخة احتياطية من ملف `config.json`

## 🎯 خطوات سريعة للبدء

1. **تثبيت** → تشغيل `LaraLabPrintAgentSetup.exe`
2. **تكوين** → اختيار الطابعة من الإعدادات
3. **اختبار** → طباعة تجريبية
4. **تشغيل** → البرنامج يعمل تلقائياً في الخلفية

🎉 **مبروك! البرنامج جاهز للاستخدام**
