#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بناء مثبت LaraLab Print Agent
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def build_executable():
    """بناء الملف التنفيذي باستخدام PyInstaller"""
    
    print("🔨 بناء LaraLab Print Agent...")
    print("=" * 50)
    
    # التأكد من وجود PyInstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller متوفر: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller غير مثبت!")
        print("قم بتثبيته باستخدام: pip install pyinstaller")
        return False
    
    # تنظيف المجلدات السابقة
    folders_to_clean = ['build', 'dist']
    for folder in folders_to_clean:
        if os.path.exists(folder):
            print(f"🧹 تنظيف مجلد {folder}...")
            shutil.rmtree(folder)
    
    # إعداد أوامر PyInstaller
    pyinstaller_cmd = [
        'pyinstaller',
        '--onefile',                    # ملف واحد
        '--windowed',                   # بدون نافذة console
        '--name=LaraLabPrintAgent',     # اسم الملف
        '--icon=icon.ico',              # أيقونة (إذا كانت متوفرة)
        '--add-data=fonts;fonts',       # إضافة مجلد الخطوط
        '--add-data=config.json;.',     # إضافة ملف الإعدادات
        '--hidden-import=win32print',   # استيراد مخفي
        '--hidden-import=win32ui',      # استيراد مخفي
        '--hidden-import=win32con',     # استيراد مخفي
        '--hidden-import=arabic_reshaper',  # استيراد مخفي
        '--hidden-import=bidi',         # استيراد مخفي
        '--hidden-import=barcode',      # استيراد مخفي
        '--hidden-import=PIL',          # استيراد مخفي
        'gui.py'                        # الملف الرئيسي
    ]
    
    # إزالة أيقونة إذا لم تكن موجودة
    if not os.path.exists('icon.ico'):
        pyinstaller_cmd = [cmd for cmd in pyinstaller_cmd if not cmd.startswith('--icon')]
    
    print("🚀 تشغيل PyInstaller...")
    try:
        result = subprocess.run(pyinstaller_cmd, check=True, capture_output=True, text=True)
        print("✅ تم بناء الملف التنفيذي بنجاح!")
        
        # التحقق من وجود الملف المبني
        exe_path = os.path.join('dist', 'LaraLabPrintAgent.exe')
        if os.path.exists(exe_path):
            file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
            print(f"📁 الملف: {exe_path}")
            print(f"📏 الحجم: {file_size:.1f} MB")
            return True
        else:
            print("❌ لم يتم العثور على الملف المبني!")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في البناء: {e}")
        print(f"الخطأ: {e.stderr}")
        return False

def create_installer():
    """إنشاء مثبت احترافي باستخدام Inno Setup"""

    print("\n📦 إنشاء المثبت الاحترافي...")

    # التحقق من وجود الملف التنفيذي
    exe_path = os.path.join('dist', 'LaraLabPrintAgent.exe')
    if not os.path.exists(exe_path):
        print("❌ الملف التنفيذي غير موجود! قم ببنائه أولاً.")
        return False

    # البحث عن Inno Setup
    inno_paths = [
        r"C:\Program Files (x86)\Inno Setup 6\ISCC.exe",
        r"C:\Program Files\Inno Setup 6\ISCC.exe",
        r"C:\Program Files (x86)\Inno Setup 5\ISCC.exe",
        r"C:\Program Files\Inno Setup 5\ISCC.exe"
    ]

    inno_compiler = None
    for path in inno_paths:
        if os.path.exists(path):
            inno_compiler = path
            break

    if inno_compiler:
        print(f"✅ تم العثور على Inno Setup: {inno_compiler}")

        # تشغيل Inno Setup لإنشاء المثبت
        try:
            result = subprocess.run([inno_compiler, 'simple_installer.iss'],
                                  check=True, capture_output=True, text=True)

            # البحث عن الملف المُنشأ
            installer_path = os.path.join('installer_output', 'LaraLabPrintAgentSetup.exe')
            if os.path.exists(installer_path):
                file_size = os.path.getsize(installer_path) / (1024 * 1024)  # MB
                print(f"✅ تم إنشاء المثبت الاحترافي!")
                print(f"📁 الملف: {installer_path}")
                print(f"📏 الحجم: {file_size:.1f} MB")

                # نسخ المثبت للمجلد الرئيسي
                final_installer = 'LaraLabPrintAgentSetup.exe'
                shutil.copy2(installer_path, final_installer)
                print(f"📦 المثبت النهائي: {final_installer}")
                return True
            else:
                print("❌ لم يتم العثور على المثبت المُنشأ!")
                return False

        except subprocess.CalledProcessError as e:
            print(f"❌ خطأ في إنشاء المثبت: {e}")
            print(f"الخطأ: {e.stderr}")
            return False
    else:
        print("⚠️  Inno Setup غير مثبت - إنشاء مثبت ZIP بديل...")
        return create_zip_installer()

def create_zip_installer():
    """إنشاء مثبت ZIP كبديل"""

    # إنشاء مجلد التوزيع
    release_dir = 'release'
    if os.path.exists(release_dir):
        shutil.rmtree(release_dir)
    os.makedirs(release_dir)

    # نسخ الملفات المطلوبة
    files_to_copy = [
        ('dist/LaraLabPrintAgent.exe', 'LaraLabPrintAgent.exe'),
        ('README.txt', 'README.txt'),
        ('TROUBLESHOOTING.md', 'TROUBLESHOOTING.md'),
        ('requirements.txt', 'requirements.txt'),
        ('simple_print_test.py', 'simple_print_test.py')
    ]

    for src, dst in files_to_copy:
        if os.path.exists(src):
            dst_path = os.path.join(release_dir, dst)
            shutil.copy2(src, dst_path)
            print(f"📄 نسخ: {src} -> {dst_path}")

    # نسخ مجلد الخطوط
    fonts_src = 'fonts'
    fonts_dst = os.path.join(release_dir, 'fonts')
    if os.path.exists(fonts_src):
        shutil.copytree(fonts_src, fonts_dst)
        print(f"📁 نسخ مجلد: {fonts_src} -> {fonts_dst}")

    # إنشاء ملف إعدادات افتراضي
    default_config = {
        "printer_name": "",
        "label_width_mm": 50,
        "label_height_mm": 25,
        "dpi": 203,
        "api_base_url": "http://localhost:8083/api",
        "PRINTER_API_KEY": "print12345678",
        "poll_interval": 3,
        "listening_port": 9898,
        "font_bold_name": "NotoNaskhArabic-Bold.ttf",
        "font_regular_name": "NotoNaskhArabic-Regular.ttf",
        "font_english_bold": "arial.ttf",
        "font_english_regular": "arial.ttf",
        "padding_mm": 0.5,
        "line_padding_px": 2,
        "section_gap_px": 6,
        "barcode_height_mm": 7.0,
        "barcode_width_factor": 1.3,
        "font_size_pt_patient": 11,
        "font_size_pt_id": 10,
        "font_size_pt_regular": 9,
        "font_size_pt_tests": 10,
        "text_color": "#000000"
    }

    import json
    config_path = os.path.join(release_dir, 'config.json')
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(default_config, f, indent=4, ensure_ascii=False)
    print(f"⚙️  إنشاء ملف الإعدادات: {config_path}")

    # إنشاء ملف تعليمات التثبيت
    install_instructions = """# تعليمات التثبيت السريع

## خطوات التثبيت:

1. فك ضغط هذا الملف في مجلد منفصل
2. تشغيل LaraLabPrintAgent.exe
3. تكوين إعدادات الطابعة من الواجهة
4. (اختياري) تشغيل add_to_startup.py لإضافة البرنامج للبدء التلقائي

## الملفات المهمة:

- LaraLabPrintAgent.exe: البرنامج الرئيسي
- config.json: ملف الإعدادات
- fonts/: الخطوط العربية المطلوبة
- simple_print_test.py: اختبار الطباعة

## الدعم:

راجع ملف TROUBLESHOOTING.md لحل المشاكل الشائعة
"""

    install_path = os.path.join(release_dir, 'تعليمات_التثبيت.txt')
    with open(install_path, 'w', encoding='utf-8') as f:
        f.write(install_instructions)
    print(f"📋 إنشاء تعليمات التثبيت: {install_path}")

    # ضغط المجلد
    try:
        import zipfile
        zip_path = 'LaraLabPrintAgent_Portable.zip'
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(release_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_path = os.path.relpath(file_path, release_dir)
                    zipf.write(file_path, arc_path)

        zip_size = os.path.getsize(zip_path) / (1024 * 1024)  # MB
        print(f"📦 تم إنشاء المثبت البديل: {zip_path}")
        print(f"📏 حجم المثبت: {zip_size:.1f} MB")
        return True

    except Exception as e:
        print(f"❌ خطأ في إنشاء المثبت: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🏗️  بناء LaraLab Print Agent")
    print("=" * 50)
    
    # التحقق من البيئة
    if not os.path.exists('gui.py'):
        print("❌ ملف gui.py غير موجود!")
        return
    
    if not os.path.exists('fonts'):
        print("❌ مجلد fonts غير موجود!")
        return
    
    # بناء الملف التنفيذي
    if build_executable():
        print("\n" + "="*50)
        
        # إنشاء المثبت
        if create_installer():
            print("\n✅ تم بناء النظام بنجاح!")
            print("📁 الملفات جاهزة في مجلد release/")
            print("📦 المثبت: LaraLabPrintAgent_Setup.zip")
        else:
            print("\n⚠️  تم بناء الملف التنفيذي لكن فشل إنشاء المثبت")
    else:
        print("\n❌ فشل في بناء النظام!")

if __name__ == "__main__":
    main()
