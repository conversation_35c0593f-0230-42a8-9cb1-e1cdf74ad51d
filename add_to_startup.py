#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة LaraLab Print Agent للبدء التلقائي مع Windows
"""

import os
import sys
import winreg
import shutil
from pathlib import Path

def add_to_startup():
    """إضافة البرنامج للبدء التلقائي"""
    
    print("🚀 إضافة LaraLab Print Agent للبدء التلقائي")
    print("=" * 50)
    
    try:
        # الحصول على مسار الملف التنفيذي الحالي
        if getattr(sys, 'frozen', False):
            # إذا كان يعمل كملف exe
            exe_path = sys.executable
        else:
            # إذا كان يعمل كسكريبت Python
            exe_path = os.path.abspath(sys.argv[0])
        
        print(f"📁 مسار البرنامج: {exe_path}")
        
        # فتح مفتاح التسجيل للبدء التلقائي
        key_path = r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run"
        
        with winreg.OpenKey(winreg.HKEY_CURRENT_USER, key_path, 0, winreg.KEY_SET_VALUE) as key:
            # إضافة البرنامج للبدء التلقائي
            winreg.SetValueEx(key, "LaraLabPrintAgent", 0, winreg.REG_SZ, exe_path)
            print("✅ تم إضافة البرنامج للبدء التلقائي بنجاح!")
            
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة البرنامج للبدء التلقائي: {e}")
        return False

def remove_from_startup():
    """إزالة البرنامج من البدء التلقائي"""
    
    print("🗑️  إزالة LaraLab Print Agent من البدء التلقائي")
    print("=" * 50)
    
    try:
        # فتح مفتاح التسجيل للبدء التلقائي
        key_path = r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run"
        
        with winreg.OpenKey(winreg.HKEY_CURRENT_USER, key_path, 0, winreg.KEY_SET_VALUE) as key:
            # إزالة البرنامج من البدء التلقائي
            try:
                winreg.DeleteValue(key, "LaraLabPrintAgent")
                print("✅ تم إزالة البرنامج من البدء التلقائي بنجاح!")
                return True
            except FileNotFoundError:
                print("ℹ️  البرنامج غير موجود في البدء التلقائي")
                return True
                
    except Exception as e:
        print(f"❌ خطأ في إزالة البرنامج من البدء التلقائي: {e}")
        return False

def check_startup_status():
    """فحص حالة البدء التلقائي"""
    
    try:
        key_path = r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run"
        
        with winreg.OpenKey(winreg.HKEY_CURRENT_USER, key_path, 0, winreg.KEY_READ) as key:
            try:
                value, _ = winreg.QueryValueEx(key, "LaraLabPrintAgent")
                print(f"✅ البرنامج موجود في البدء التلقائي: {value}")
                return True
            except FileNotFoundError:
                print("❌ البرنامج غير موجود في البدء التلقائي")
                return False
                
    except Exception as e:
        print(f"❌ خطأ في فحص حالة البدء التلقائي: {e}")
        return False

def create_desktop_shortcut():
    """إنشاء اختصار على سطح المكتب"""
    
    print("🖥️  إنشاء اختصار على سطح المكتب")
    
    try:
        import win32com.client
        
        # الحصول على مسار سطح المكتب
        desktop = winreg.QueryValueEx(
            winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Software\Microsoft\Windows\CurrentVersion\Explorer\Shell Folders"),
            "Desktop"
        )[0]
        
        # مسار الاختصار
        shortcut_path = os.path.join(desktop, "LaraLab Print Agent.lnk")
        
        # الحصول على مسار الملف التنفيذي
        if getattr(sys, 'frozen', False):
            exe_path = sys.executable
        else:
            exe_path = os.path.abspath(sys.argv[0])
        
        # إنشاء الاختصار
        shell = win32com.client.Dispatch("WScript.Shell")
        shortcut = shell.CreateShortCut(shortcut_path)
        shortcut.Targetpath = exe_path
        shortcut.WorkingDirectory = os.path.dirname(exe_path)
        shortcut.Description = "LaraLab Print Agent - وكيل طباعة لارا لاب"
        shortcut.save()
        
        print(f"✅ تم إنشاء الاختصار: {shortcut_path}")
        return True
        
    except ImportError:
        print("⚠️  مكتبة win32com غير متوفرة - لا يمكن إنشاء اختصار")
        return False
    except Exception as e:
        print(f"❌ خطأ في إنشاء الاختصار: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("⚙️  إعداد LaraLab Print Agent")
    print("=" * 50)
    
    while True:
        print("\nاختر العملية:")
        print("1. إضافة للبدء التلقائي")
        print("2. إزالة من البدء التلقائي") 
        print("3. فحص حالة البدء التلقائي")
        print("4. إنشاء اختصار على سطح المكتب")
        print("5. خروج")
        
        choice = input("\nاختيارك (1-5): ").strip()
        
        if choice == "1":
            add_to_startup()
        elif choice == "2":
            remove_from_startup()
        elif choice == "3":
            check_startup_status()
        elif choice == "4":
            create_desktop_shortcut()
        elif choice == "5":
            print("👋 وداعاً!")
            break
        else:
            print("❌ اختيار غير صحيح!")

if __name__ == "__main__":
    main()
