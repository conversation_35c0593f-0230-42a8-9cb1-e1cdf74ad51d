[Setup]
; معلومات أساسية
AppName=LaraLab Print Agent
AppVersion=1.0
AppPublisher=LaraLab
AppPublisherURL=https://laralab.com
AppSupportURL=https://laralab.com/support
AppUpdatesURL=https://laralab.com/updates
DefaultDirName={autopf}\LaraLab Print Agent
DefaultGroupName=LaraLab Print Agent
AllowNoIcons=yes
LicenseFile=
InfoBeforeFile=
InfoAfterFile=
OutputDir=installer_output
OutputBaseFilename=LaraLabPrintAgentSetup
SetupIconFile=
Compression=lzma
SolidCompression=yes
WizardStyle=modern
PrivilegesRequired=admin
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

; واجهة المثبت
WizardImageFile=
WizardSmallImageFile=
DisableWelcomePage=no
DisableDirPage=no
DisableProgramGroupPage=no
DisableReadyPage=no
DisableFinishedPage=no

; إعدادات إضافية
UninstallDisplayIcon={app}\LaraLabPrintAgent.exe
UninstallDisplayName=LaraLab Print Agent
CreateUninstallRegKey=yes
UpdateUninstallLogAppName=yes

[Languages]
Name: "arabic"; MessagesFile: "compiler:Languages\Arabic.isl"
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 0,6.1
Name: "startup"; Description: "تشغيل تلقائي مع Windows"; GroupDescription: "خيارات إضافية:"; Flags: checked

[Files]
; الملف التنفيذي الرئيسي
Source: "dist\LaraLabPrintAgent.exe"; DestDir: "{app}"; Flags: ignoreversion

; الخطوط العربية
Source: "fonts\*"; DestDir: "{app}\fonts"; Flags: ignoreversion recursesubdirs createallsubdirs

; ملفات الإعدادات والوثائق
Source: "config.json"; DestDir: "{app}"; Flags: ignoreversion
Source: "README_USER.txt"; DestDir: "{app}"; Flags: ignoreversion
Source: "TROUBLESHOOTING.md"; DestDir: "{app}"; Flags: ignoreversion
Source: "requirements.txt"; DestDir: "{app}"; Flags: ignoreversion

; ملف اختبار الطباعة
Source: "simple_print_test.py"; DestDir: "{app}"; Flags: ignoreversion

; ملف إضافة للبدء التلقائي
Source: "add_to_startup.py"; DestDir: "{app}"; Flags: ignoreversion

[Icons]
; اختصار في قائمة البرامج
Name: "{group}\LaraLab Print Agent"; Filename: "{app}\LaraLabPrintAgent.exe"
Name: "{group}\اختبار الطباعة"; Filename: "python.exe"; Parameters: """{app}\simple_print_test.py"""
Name: "{group}\دليل الاستخدام"; Filename: "{app}\README_USER.txt"
Name: "{group}\حل المشاكل"; Filename: "{app}\TROUBLESHOOTING.md"
Name: "{group}\إعداد البدء التلقائي"; Filename: "python.exe"; Parameters: """{app}\add_to_startup.py"""
Name: "{group}\{cm:UninstallProgram,LaraLab Print Agent}"; Filename: "{uninstallexe}"

; اختصار على سطح المكتب
Name: "{autodesktop}\LaraLab Print Agent"; Filename: "{app}\LaraLabPrintAgent.exe"; Tasks: desktopicon

; اختصار في شريط المهام السريع
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\LaraLab Print Agent"; Filename: "{app}\LaraLabPrintAgent.exe"; Tasks: quicklaunchicon

[Registry]
; إضافة للبدء التلقائي إذا اختار المستخدم ذلك
Root: HKCU; Subkey: "SOFTWARE\Microsoft\Windows\CurrentVersion\Run"; ValueType: string; ValueName: "LaraLabPrintAgent"; ValueData: "{app}\LaraLabPrintAgent.exe"; Tasks: startup

[Run]
; تشغيل البرنامج بعد التثبيت
Filename: "{app}\LaraLabPrintAgent.exe"; Description: "{cm:LaunchProgram,LaraLab Print Agent}"; Flags: nowait postinstall skipifsilent

[UninstallRun]
; إزالة من البدء التلقائي عند الإلغاء
Filename: "{cmd}"; Parameters: "/c reg delete ""HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Run"" /v ""LaraLabPrintAgent"" /f"; Flags: runhidden

[Code]
// كود Pascal للتحكم في المثبت

// فحص إذا كان البرنامج يعمل حالياً
function IsAppRunning(): Boolean;
var
  FWMIService: Variant;
  FSWbemLocator: Variant;
  FWbemObjectSet: Variant;
begin
  Result := false;
  try
    FSWbemLocator := CreateOleObject('WbemScripting.SWbemLocator');
    FWMIService := FSWbemLocator.ConnectServer('', 'root\CIMV2', '', '');
    FWbemObjectSet := FWMIService.ExecQuery('SELECT Name FROM Win32_Process WHERE Name="LaraLabPrintAgent.exe"');
    Result := (FWbemObjectSet.Count > 0);
  except
    Result := false;
  end;
end;

// التحقق قبل التثبيت
function InitializeSetup(): Boolean;
begin
  Result := True;
  if IsAppRunning() then
  begin
    if MsgBox('LaraLab Print Agent يعمل حالياً. يجب إغلاقه قبل المتابعة.' + #13#10 + 'هل تريد إغلاقه الآن؟', 
              mbConfirmation, MB_YESNO) = IDYES then
    begin
      // محاولة إغلاق البرنامج
      Exec('taskkill', '/f /im LaraLabPrintAgent.exe', '', SW_HIDE, ewWaitUntilTerminated, ResultCode);
      Sleep(2000); // انتظار ثانيتين
      
      // التحقق مرة أخرى
      if IsAppRunning() then
      begin
        MsgBox('لم يتم إغلاق البرنامج. يرجى إغلاقه يدوياً والمحاولة مرة أخرى.', mbError, MB_OK);
        Result := False;
      end;
    end
    else
      Result := False;
  end;
end;

// رسالة ترحيب مخصصة
procedure InitializeWizard();
begin
  WizardForm.WelcomeLabel1.Caption := 'مرحباً بك في مثبت LaraLab Print Agent';
  WizardForm.WelcomeLabel2.Caption := 'سيقوم هذا المثبت بتثبيت LaraLab Print Agent على جهازك.' + #13#10 + #13#10 +
    'LaraLab Print Agent هو برنامج لطباعة ملصقات العينات الطبية تلقائياً.' + #13#10 + #13#10 +
    'انقر على التالي للمتابعة.';
end;

// التحقق من المتطلبات
function NextButtonClick(CurPageID: Integer): Boolean;
begin
  Result := True;
  
  if CurPageID = wpSelectDir then
  begin
    // التحقق من وجود مساحة كافية (50 MB على الأقل)
    if GetSpaceOnDisk(WizardDirValue) < 50 * 1024 * 1024 then
    begin
      MsgBox('لا توجد مساحة كافية على القرص المحدد. يحتاج البرنامج إلى 50 ميجابايت على الأقل.', mbError, MB_OK);
      Result := False;
    end;
  end;
end;

// رسالة الانتهاء
procedure CurStepChanged(CurStep: TSetupStep);
begin
  if CurStep = ssPostInstall then
  begin
    // إنشاء ملف إعدادات افتراضي إذا لم يكن موجوداً
    if not FileExists(ExpandConstant('{app}\config.json')) then
    begin
      // يمكن إضافة كود لإنشاء ملف إعدادات افتراضي هنا
    end;
  end;
end;
