import requests
import json
import platform
import sys
import os
import traceback
from label_design import create_compact_label_image
import win32con

if platform.system() == "Windows":
    import win32print
    import win32ui
    from PIL import ImageWin
else:
    raise RuntimeError("هذا البرنامج يعمل فقط على ويندوز")

# استخدام مجلد AppData كما في gui.py
if platform.system() == "Windows":
    CONFIG_DIR = os.path.join(os.environ["APPDATA"], "LaraLabPrintAgent")
else:
    CONFIG_DIR = os.path.join(os.path.expanduser("~"), ".laralabprintagent")
    
if not os.path.exists(CONFIG_DIR):
    os.makedirs(CONFIG_DIR)
    
CONFIG_FILE = os.path.join(CONFIG_DIR, "config.json")

def resource_path(relative_path):
    try:
        base_path = sys._MEIPASS
    except AttributeError:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

def load_config():
    try:
        with open(CONFIG_FILE, "r", encoding="utf-8") as f:
            config = json.load(f)
            # التأكد من وجود مفاتيح افتراضية جديدة
            if 'listening_port' not in config:
                config['listening_port'] = 9898
        return config
    except Exception as e:
        print(f"❌ خطأ في قراءة ملف الإعدادات ({CONFIG_FILE}): {e}")
        # إعدادات افتراضية إذا لم يمكن قراءة الملف
        default_config = {
            "printer_name": "",
            "label_width_mm": 28,
            "label_height_mm": 24,
            "dpi": 300,
            "listening_port": 9898,
        }
        return default_config

def check_printer(printer_name):
    printers = [printer[2] for printer in win32print.EnumPrinters(2)]
    return printer_name in printers, printers

def check_fonts(font_family):
    font_path = resource_path(os.path.join("fonts", font_family))
    return os.path.exists(font_path)

def print_image(image, printer_name, log_queue, config=None):
    if not printer_name:
        log_queue.put("ERROR: ❌ اسم الطابعة فارغ - تأكد من تحديد الطابعة في الإعدادات")
        return False
    
    # تحقق من وجود الطابعة
    printers = [printer[2] for printer in win32print.EnumPrinters(2)]
    if printer_name not in printers:
        log_queue.put(f"ERROR: ❌ الطابعة '{printer_name}' غير متاحة أو مطفأة!")
        available_printers = ", ".join(printers)
        log_queue.put(f"INFO: الطابعات المتاحة: {available_printers}")
        return False
    
    try:
        # فتح الطابعة والحصول على سياق الجهاز
        hprinter = win32print.OpenPrinter(printer_name)
        pdc = win32ui.CreateDC()
        pdc.CreatePrinterDC(printer_name)
        
        # الحصول على معلومات الطابعة الفعلية
        printer_dpi_x = pdc.GetDeviceCaps(win32con.LOGPIXELSX)
        printer_dpi_y = pdc.GetDeviceCaps(win32con.LOGPIXELSY)
        physical_width = pdc.GetDeviceCaps(win32con.PHYSICALWIDTH)
        physical_height = pdc.GetDeviceCaps(win32con.PHYSICALHEIGHT)
        printable_width = pdc.GetDeviceCaps(win32con.HORZRES)
        printable_height = pdc.GetDeviceCaps(win32con.VERTRES)
        
        # الأبعاد المطلوبة بالملليمتر
        TARGET_WIDTH_MM = 50.0
        TARGET_HEIGHT_MM = 25.0
        
        # تحويل الأبعاد المطلوبة إلى نقاط الطابعة
        width_printer_px = int(TARGET_WIDTH_MM * printer_dpi_x / 25.4)
        height_printer_px = int(TARGET_HEIGHT_MM * printer_dpi_y / 25.4)
        
        # حساب عوامل التحجيم
        scale_x = width_printer_px / image.width
        scale_y = height_printer_px / image.height
        
        try:
            # بدء مهمة الطباعة
            pdc.StartDoc("Sticker Print")
            pdc.StartPage()

            # تحسين جودة الطباعة للنصوص العربية (مبسط)
            dib = ImageWin.Dib(image)

            # إعداد جودة الطباعة
            pdc.SetMapMode(win32con.MM_TEXT)

            # حل مشكلة المنطقة غير القابلة للطباعة - إضافة مارجن للطباعة
            printer_margin_mm = config.get('printer_margin_mm', 0.1) if config else 0.1
            printer_margin_px = int(printer_dpi_x * printer_margin_mm / 25.4)

            # طباعة بدون تمديد إذا كانت الأبعاد متطابقة تقريباً
            if abs(image.width - width_printer_px) < 10 and abs(image.height - height_printer_px) < 10:
                # طباعة مباشرة مع مارجن آمن
                safe_x = printer_margin_px
                safe_y = printer_margin_px
                safe_width = image.width - (2 * printer_margin_px)
                safe_height = image.height - (2 * printer_margin_px)
                dib.draw(pdc.GetHandleOutput(), (safe_x, safe_y, safe_x + safe_width, safe_y + safe_height))
                log_queue.put("DEBUG: طباعة مباشرة مع مارجن آمن للطابعة")
            else:
                # طباعة مع تحجيم ومارجن آمن
                safe_x = printer_margin_px
                safe_y = printer_margin_px
                safe_width = width_printer_px - (2 * printer_margin_px)
                safe_height = height_printer_px - (2 * printer_margin_px)
                dib.draw(pdc.GetHandleOutput(), (safe_x, safe_y, safe_x + safe_width, safe_y + safe_height))
                log_queue.put("DEBUG: طباعة مع تحجيم ومارجن آمن للطابعة")
            
            pdc.EndPage()
            pdc.EndDoc()
            pdc.DeleteDC()
            
            # تسجيل معلومات تشخيصية
            log_queue.put(f"INFO: ✅ تمت الطباعة بنجاح على '{printer_name}'")
            log_queue.put(f"DEBUG: الأبعاد المطلوبة: {TARGET_WIDTH_MM:.1f}mm × {TARGET_HEIGHT_MM:.1f}mm")
            log_queue.put(f"DEBUG: دقة الطابعة: {printer_dpi_x}×{printer_dpi_y} DPI")
            log_queue.put(f"DEBUG: مارجن الطابعة المطبق: {printer_margin_px} بكسل ({printer_margin_mm:.1f}mm)")
            log_queue.put(f"DEBUG: أبعاد الصورة: {image.width}×{image.height} بكسل")
            log_queue.put(f"DEBUG: أبعاد الطابعة: {width_printer_px}×{height_printer_px} بكسل")
            log_queue.put(f"DEBUG: أبعاد الطباعة الفعلية: {width_printer_px/printer_dpi_x*25.4:.1f}mm × {height_printer_px/printer_dpi_y*25.4:.1f}mm")
            log_queue.put(f"DEBUG: منطقة الطباعة المتاحة: {printable_width/printer_dpi_x*25.4:.1f}mm × {printable_height/printer_dpi_y*25.4:.1f}mm")
            return True
            
        finally:
            win32print.ClosePrinter(hprinter)
            
    except Exception as e:
        error_details = traceback.format_exc()
        log_queue.put(f"ERROR: ❌ خطأ في الطباعة: {e}")
        log_queue.put(f"DEBUG: تفاصيل الخطأ: {error_details}")
        return False

def process_print_request(data, config, log_queue):
    """
    Processes a full print request received from the server.
    Iterates through sample groups and prints one label for each.
    """
    all_successful = True
    try:
        lab_order_info = data['labOrder']
        sample_groups_dict = data['sampleGroups']
        
        # التأكد من أن sampleGroups هو قاموس
        if not isinstance(sample_groups_dict, dict):
            log_queue.put(f"ERROR: ❌ الحقل 'sampleGroups' يجب أن يكون كائن (dictionary)، لكن تم استلام نوع: {type(sample_groups_dict)}")
            return False

        # تحويل قيم القاموس إلى قائمة للمعالجة
        sample_groups = list(sample_groups_dict.values())
        printer_name = config.get("printer_name")
        
        # تحويل الأبعاد من mm إلى بكسل - استخدام دقة الطابعة الفعلية
        # الحصول على دقة الطابعة أولاً
        hprinter = win32print.OpenPrinter(printer_name)
        pdc = win32ui.CreateDC()
        pdc.CreatePrinterDC(printer_name)
        printer_dpi_x = pdc.GetDeviceCaps(win32con.LOGPIXELSX)
        printer_dpi_y = pdc.GetDeviceCaps(win32con.LOGPIXELSY)
        pdc.DeleteDC()
        win32print.ClosePrinter(hprinter)

        # استخدام دقة الطابعة الفعلية لإنشاء الصورة
        actual_dpi = min(printer_dpi_x, printer_dpi_y)  # استخدام أقل دقة للتوافق
        config_with_printer_dpi = config.copy()
        config_with_printer_dpi["dpi"] = actual_dpi

        log_queue.put(f"INFO: استخدام دقة الطابعة الفعلية: {actual_dpi} DPI")

        log_queue.put(f"INFO: بدء معالجة طلب طباعة يحتوي على {len(sample_groups)} ملصق(ات).")

        for i, group in enumerate(sample_groups):
            sample_code = group.get('sample_code', f"ملصق {i+1}")
            log_queue.put(f"INFO: -> جاري إنشاء ملصق لـ: {sample_code}")
            
            # إنشاء صورة الملصق باستخدام دقة الطابعة الفعلية
            label_image = create_compact_label_image(
                lab_order_info=lab_order_info,
                sample_group=group,
                config=config_with_printer_dpi
            )
            
            # طباعة الصورة
            if not print_image(label_image, printer_name, log_queue, config):
                log_queue.put(f"ERROR: ❌ فشلت طباعة الملصق الخاص بـ {sample_code}.")
                all_successful = False
                # يمكن أن تقرر إيقاف الطباعة عند أول خطأ أو الاستمرار
                # break 

        if all_successful:
            log_queue.put("INFO: ✅ اكتملت جميع مهام الطباعة بنجاح.")
        else:
            log_queue.put("WARN: ⚠️ اكتملت معالجة الطلب مع وجود أخطاء في بعض الملصقات.")

    except KeyError as e:
        log_queue.put(f"ERROR: ❌ خطأ في البيانات المستلمة، الحقل المتوقع مفقود: {e}")
        all_successful = False
    except Exception as e:
        log_queue.put(f"ERROR: ❌ حدث خطأ غير متوقع أثناء إنشاء الملصق: {e}")
        log_queue.put(f"DEBUG: {traceback.format_exc()}")
        all_successful = False
        
    return all_successful 