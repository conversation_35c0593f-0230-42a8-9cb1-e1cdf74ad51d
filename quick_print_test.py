#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار طباعة سريع للملصق المحسن
"""

import json
from datetime import datetime
from label_design import create_compact_label_image
from printer_core import print_image
import queue

def quick_print_test():
    """اختبار طباعة سريع"""
    
    # قراءة الإعدادات
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
    except:
        print("❌ لا يمكن قراءة ملف الإعدادات")
        return False
    
    # إنشاء queue للسجلات
    log_queue = queue.Queue()
    
    # بيانات تجريبية
    lab_order_info = {
        'patient': {
            'name': 'أحمد محمد عبد الله الطويل'  # اسم طويل لاختبار التحسين
        },
        'created_at_raw': datetime.now().isoformat() + 'Z'
    }
    
    sample_group = {
        'sample_code': 'TEST001',
        'name': 'Blood',
        'tests': [
            {'name': 'CBC'},
            {'name': 'ESR'},
            {'name': 'CRP'},
            {'name': 'Glucose'},
            {'name': 'Creatinine'},
            {'name': 'Urea'},
            {'name': 'ALT'},
            {'name': 'AST'},
            {'name': 'Bilirubin'},
            {'name': 'Total Protein'}  # عدد جيد من التحاليل للاختبار
        ]
    }
    
    print("🖨️ بدء اختبار الطباعة السريع...")
    
    try:
        # إنشاء صورة الملصق
        print("📝 إنشاء الملصق...")
        label_image = create_compact_label_image(
            lab_order_info=lab_order_info,
            sample_group=sample_group,
            config=config
        )
        
        # حفظ نسخة للمراجعة
        label_image.save("quick_test_label.png")
        print("💾 تم حفظ نسخة من الملصق: quick_test_label.png")
        
        # الحصول على اسم الطابعة من الإعدادات
        printer_name = config.get('printer_name', '')
        if not printer_name:
            print("❌ لم يتم تحديد اسم الطابعة في الإعدادات")
            return False
        
        print(f"🖨️ الطباعة على: {printer_name}")
        
        # طباعة الملصق
        success = print_image(label_image, printer_name, log_queue)
        
        # عرض السجلات
        while not log_queue.empty():
            print(log_queue.get())
        
        if success:
            print("✅ تم اختبار الطباعة بنجاح!")
            return True
        else:
            print("❌ فشل في اختبار الطباعة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الطباعة: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 اختبار طباعة سريع للملصق المحسن...")
    success = quick_print_test()
    
    if success:
        print("\n🎉 تم الاختبار بنجاح!")
        print("✨ التحسينات المطبقة:")
        print("   • تحسين موضع اسم المريض")
        print("   • تقليل الفراغات بين التحاليل")
        print("   • استخدام مسافات بسيطة بين التحاليل")
    else:
        print("\n⚠️ يرجى التحقق من إعدادات الطابعة")
