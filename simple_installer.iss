[Setup]
AppName=LaraLab Print Agent
AppVersion=1.0
AppPublisher=LaraLab
DefaultDirName={autopf}\LaraLab Print Agent
DefaultGroupName=LaraLab Print Agent
OutputDir=installer_output
OutputBaseFilename=LaraLabPrintAgentSetup
Compression=lzma
SolidCompression=yes
WizardStyle=modern
PrivilegesRequired=admin

[Files]
Source: "dist\LaraLabPrintAgent.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "fonts\*"; DestDir: "{app}\fonts"; Flags: ignoreversion recursesubdirs createallsubdirs
Source: "config.json"; DestDir: "{app}"; Flags: ignoreversion
Source: "README_USER.txt"; DestDir: "{app}"; Flags: ignoreversion
Source: "TROUBLESHOOTING.md"; DestDir: "{app}"; Flags: ignoreversion
Source: "simple_print_test.py"; DestDir: "{app}"; Flags: ignoreversion
Source: "add_to_startup.py"; DestDir: "{app}"; Flags: ignoreversion

[Icons]
Name: "{group}\LaraLab Print Agent"; Filename: "{app}\LaraLabPrintAgent.exe"
Name: "{group}\دليل الاستخدام"; Filename: "{app}\README_USER.txt"
Name: "{group}\{cm:UninstallProgram,LaraLab Print Agent}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\LaraLab Print Agent"; Filename: "{app}\LaraLabPrintAgent.exe"

[Tasks]
Name: "desktopicon"; Description: "Create desktop shortcut"; GroupDescription: "Additional options:"
Name: "startup"; Description: "Start with Windows"; GroupDescription: "Additional options:"

[Registry]
Root: HKCU; Subkey: "SOFTWARE\Microsoft\Windows\CurrentVersion\Run"; ValueType: string; ValueName: "LaraLabPrintAgent"; ValueData: "{app}\LaraLabPrintAgent.exe"; Tasks: startup

[Run]
Filename: "{app}\LaraLabPrintAgent.exe"; Description: "تشغيل LaraLab Print Agent"; Flags: nowait postinstall skipifsilent

[UninstallRun]
Filename: "{cmd}"; Parameters: "/c reg delete ""HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Run"" /v ""LaraLabPrintAgent"" /f"; Flags: runhidden
